import StandardLayout from '@/layouts/Standard.vue';
import { ACCESS_CODE, PRODUCTKEY_TOWER } from '@/config';

import Call from '@/tower/Call.vue';
import Calls from '@/tower/Calls.vue';
import CallReel from '@/tower/CallReel.vue';
import AddCall from '@/tower/AddCall.vue';
import AssignCall from '@/tower/AssignCall.vue';
import Dispatch from '@/tower/dispatch/Dispatch.vue';

const towerRoutes = {
  path: '/tower',
  components: { default: StandardLayout },
  meta: { product: PRODUCTKEY_TOWER },
  children: [
    {
      path: 'dispatch',
      label: 'Dispatch',
      name: 'Dispatch',
      component: Dispatch,
      meta: {
        navigatorContext: 'primary',
        accessRight: ACCESS_CODE.dispatches.read
      }
    },
    {
      path: 'calls',
      label: 'Calls',
      name: 'Calls',
      component: Calls,
      meta: {
        navigatorContext: 'primary',
        accessRight: ACCESS_CODE.calls.read
      }
    },
    {
      path: 'calls/add',
      name: 'AddCall',
      component: AddCall,
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.calls.read
      }
    },
    {
      path: 'calls/:key/edit',
      name: 'Call',
      component: Call,
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.calls.read,
        noun: 'Call',
        verb: 'Read'
      }
    },
    {
      path: 'calls-reel/:key/edit',
      name: 'CallReel',
      component: CallReel,
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.calls.read,
        noun: 'Call',
        verb: 'Read'
      }
    },
    {
      path: 'calls/:key/assign',
      name: 'AssignCall',
      component: AssignCall,
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.calls.read
      }
    },
    {
      path: 'calls/tools',
      name: 'CallTools',
      component: () => import('@/tower/CallTools.vue'),
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.calls.read
      }
    },
    {
      path: 'customers',
      label: 'Customers',
      name: 'Customers',
      component: () => import('@/tower/Customers.vue'),
      meta: {
        navigatorContext: 'primary',
        accessRight: ACCESS_CODE.customers.read
      }
    },
    {
      path: 'customers/add',
      name: 'AddCustomer',
      component: () => import('@/tower/Customer.vue'),
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.customers.edit
      }
    },
    {
      path: 'customers/:key/edit',
      name: 'Customer',
      component: () => import('@/tower/Customer.vue'),
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.customers.read,
        noun: 'Customer',
        verb: 'Read'
      }
    },
    {
      path: 'drivers',
      label: 'Drivers',
      name: 'Drivers',
      component: () => import('@/tower/Drivers.vue'),
      meta: {
        navigatorContext: 'primary',
        accessRight: ACCESS_CODE.drivers.read
      }
    },
    {
      path: 'drivers/add',
      name: 'AddDriver',
      component: () => import('@/tower/Driver.vue'),
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.drivers.read
      }
    },
    {
      path: 'drivers/:key/edit',
      name: 'Driver',
      component: () => import('@/tower/Driver.vue'),
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.drivers.read,
        noun: 'Driver',
        verb: 'Read'
      }
    },
    {
      path: 'trucks',
      label: 'Trucks',
      name: 'Trucks',
      component: () => import('@/tower/Trucks.vue'),
      meta: {
        navigatorContext: 'primary',
        accessRight: ACCESS_CODE.trucks.read
      }
    },
    {
      path: 'trucks/add',
      name: 'AddTruck',
      component: () => import('@/tower/Truck.vue'),
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.trucks.read
      }
    },
    {
      path: 'trucks/:key/edit',
      name: 'Truck',
      component: () => import('@/tower/Truck.vue'),
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.trucks.read,
        noun: 'Truck',
        verb: 'Read'
      }
    },
    {
      path: 'reports',
      label: 'Reports',
      name: 'Reports',
      component: () => import('@/tower/reports/Index.vue'),
      meta: {
        navigatorContext: 'primary',
        accessRight: ACCESS_CODE.reports.read
      }
    },
    {
      path: 'quotes',
      label: 'Quotes',
      name: 'Quotes',
      component: () => import('@/tower/Quotes.vue'),
      meta: {
        navigatorContext: 'primary',
        accessRight: ACCESS_CODE.quote.read
      }
    },
    {
      path: 'quotes/add',
      name: 'AddQuote',
      component: () => import('@/tower/Quote.vue'),
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.quote.write
      }
    },
    {
      path: 'quotes/:key/edit',
      name: 'Quote',
      component: () => import('@/tower/Quote.vue'),
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.quote.read
      }
    },
    {
      path: 'employees',
      label: 'Employees',
      name: 'Employees',
      component: () => import('@/tower/Employees.vue'),
      meta: {
        navigatorContext: 'maintenance',
        accessRight: ACCESS_CODE.employees.read
      }
    },
    {
      path: 'employees/add',
      name: 'AddEmployee',
      component: () => import('@/tower/Employee.vue'),
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.employees.edit
      }
    },
    {
      path: 'employees/:key/edit',
      name: 'Employee',
      component: () => import('@/tower/Employee.vue'),
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.employees.read,
        noun: 'Employee',
        verb: 'Read'
      }
    },
    {
      path: 'lots',
      label: 'Lots',
      name: 'Lots',
      component: () => import('@/tower/Lots.vue'),
      meta: {
        navigatorContext: 'maintenance',
        accessRight: ACCESS_CODE.lots.read
      }
    },
    {
      path: 'lots/add',
      name: 'AddLot',
      component: () => import('@/tower/Lot.vue'),
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.lots.edit
      }
    },
    {
      path: 'lots/:key/edit',
      name: 'Lot',
      component: () => import('@/tower/Lot.vue'),
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.lots.read,
        noun: 'StorageLot',
        verb: 'Read'
      }
    },
    {
      path: 'checkout',
      label: 'Checkout',
      name: 'Checkout',
      component: () => import('@/tower/Checkout.vue'),
      meta: {
        navigatorContext: 'maintenance',
        accessRight: ACCESS_CODE.checkout.read
      }
    },
    {
      path: 'liens',
      label: 'Liens',
      name: 'Liens',
      component: () => import('@/tower/liens/Index.vue'),
      meta: {
        navigatorContext: 'maintenance',
        accessRight: ACCESS_CODE.liens.readBatch
      }
    },
    {
      path: 'tickets',
      label: 'Tickets',
      name: 'Tickets',
      component: () => import('@/tower/Tickets.vue'),
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.tickets.read
      }
    },
    {
      path: 'tickets/add',
      name: 'AddTicket',
      component: () => import('@/tower/Ticket.vue'),
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.tickets.add
      }
    },
    {
      path: 'tickets/:key/edit',
      label: 'Ticket',
      name: 'Ticket',
      component: () => import('@/tower/Ticket.vue'),
      meta: {
        navigatorContext: null,
        accessRight: ACCESS_CODE.tickets.read,
        noun: 'TowTicket',
        verb: 'Read'
      }
    },
    {
      path: 'tickets/tools',
      label: 'Tickets',
      name: 'TicketTools',
      component: () => import('@/tower/TicketTools.vue'),
      meta: {
        navigatorContext: 'maintenance',
        accessRight: ACCESS_CODE.tickets.edit
      }
    },
    {
      path: 'motor-club-billing',
      label: 'Motor Club Billing',
      name: 'MotorClubBilling',
      component: () => import('@/tower/MotorClubBilling.vue'),
      meta: {
        navigatorContext: 'maintenance',
        accessRight: ACCESS_CODE.motorClubBilling.read
      }
    },
    {
      path: 'prices',
      label: 'Prices',
      name: 'Prices',
      component: () => import('@/tower/prices/Index.vue'),
      meta: {
        navigatorContext: 'maintenance',
        accessRight: ACCESS_CODE.price.read
      }
    },
    {
      path: 'payments',
      label: 'Payments',
      name: 'Payments',
      component: () => import('@/tower/Payments.vue'),
      meta: {
        navigatorContext: 'maintenance',
        accessRight: ACCESS_CODE.payments.edit
      }
    }
  ]
};

export default towerRoutes;
