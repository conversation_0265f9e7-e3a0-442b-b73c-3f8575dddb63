# Job Offers Component

A Vue component for displaying job offers and messages from the TOPS dispatch system.

## Usage

```vue
<template>
  <JobOffers 
    :poll-interval="30000" 
    :auto-start="true" 
  />
</template>

<script>
import JobOffers from './dispatch/JobOffers.vue';

export default {
  components: {
    JobOffers
  }
};
</script>
```

## Props

- `pollInterval` (Number): Polling interval in milliseconds (default: 30000)
- `autoStart` (Boolean): Whether to start polling automatically (default: true)

## Features

- ✅ Polls `Dispatches.GetNewJobsAndMessages` API
- ✅ Displays job offers with details and actions
- ✅ Shows urgent messages with acknowledgment
- ✅ Supports job action confirmation dialogs
- ✅ Real-time updates with auto-refresh
- ✅ Error handling and loading states
- ✅ Responsive design

## API Integration

The component integrates with these TOPS APIs:

- `Dispatches.GetNewJobsAndMessages` - Fetch jobs and messages
- `Job.GetDetails` - Get detailed job information
- `Jobs.GetPossibleActions` - Get available actions for a job
- `Jobs.HandleAction` - Execute job actions
- `Message.Acknowledge` - Mark messages as read

## Store Structure

Uses Pinia store with the following state:

```js
{
  jobs: [],           // Array of job offers
  messages: [],       // Array of urgent messages
  selectedJob: null,  // Currently selected job key
  jobDetails: {},     // Job details cache
  jobActions: {},     // Job actions cache
  loading: false,     // Loading state
  error: null         // Error message
}
```

## Testing

Run tests with:
```bash
pnpm test src/components/tower/dispatch
```

Tests cover:
- Response transformation
- Store functionality
- API integration
- Error handling
