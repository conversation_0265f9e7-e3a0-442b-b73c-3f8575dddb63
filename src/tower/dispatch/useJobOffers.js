import { ref, computed, onMounted, onUnmounted, unref } from 'vue';
import { useJobOffersStore } from './jobOffers.store';

export function useJobOffers(options = {}) {
  // Handle both reactive refs and plain values
  const pollInterval = unref(options.pollInterval) ?? 30000; // 30 seconds default
  const autoStart = unref(options.autoStart) ?? true;

  const store = useJobOffersStore();
  const pollTimer = ref(null);
  const isPolling = ref(false);

  // Start polling
  const startPolling = () => {
    if (isPolling.value) return;

    isPolling.value = true;
    loadJobs(); // Initial load

    if (pollInterval > 0) {
      pollTimer.value = setInterval(() => {
        loadJobs();
      }, pollInterval);
    }
  };

  // Stop polling
  const stopPolling = () => {
    if (pollTimer.value) {
      clearInterval(pollTimer.value);
      pollTimer.value = null;
    }
    isPolling.value = false;
  };

  // Load jobs and messages
  const loadJobs = async () => {
    try {
      return await store.fetchNewOffers();
    } catch (error) {
      console.error('Failed to load job offers:', error);
      throw error;
    }
  };

  // Get job details
  const getJobDetails = async (jobKey) => {
    try {
      return await store.getJobDetails(jobKey);
    } catch (error) {
      console.error('Failed to get job details:', error);
      throw error;
    }
  };

  // Get job actions
  const getJobActions = async (jobKey) => {
    try {
      return await store.getJobActions(jobKey);
    } catch (error) {
      console.error('Failed to get job actions:', error);
      throw error;
    }
  };

  // Handle job action
  const handleJobAction = async (jobKey, responseTag, responseValue) => {
    try {
      const result = await store.handleJobAction(jobKey, responseTag, responseValue);

      // Refresh jobs after action
      await loadJobs();

      return result;
    } catch (error) {
      console.error('Failed to handle job action:', error);
      throw error;
    }
  };

  // Acknowledge message
  const acknowledgeMessage = async (messageKey) => {
    try {
      return await store.acknowledgeMessage(messageKey);
    } catch (error) {
      console.error('Failed to acknowledge message:', error);
      throw error;
    }
  };

  // Select job
  const selectJob = (jobKey) => {
    store.selectJob(jobKey);
  };

  // Clear error
  const clearError = () => {
    store.clearError();
  };

  // Lifecycle management
  onMounted(() => {
    if (autoStart) {
      startPolling();
    }
  });

  onUnmounted(() => {
    stopPolling();
  });

  return {
    // State
    jobs: computed(() => store.jobs),
    messages: computed(() => store.messages),
    selectedJob: computed(() => store.selectedJob),
    loading: computed(() => store.loading),
    error: computed(() => store.error),
    isPolling,

    // Getters
    getJobDetails: store.getJobDetails,
    getJobActions: store.getJobActions,
    hasError: computed(() => store.hasError),
    isLoading: computed(() => store.isLoading),

    // Methods
    loadJobs,
    getJobDetails,
    getJobActions,
    handleJobAction,
    acknowledgeMessage,
    selectJob,
    clearError,
    startPolling,
    stopPolling
  };
}
