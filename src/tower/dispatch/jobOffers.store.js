import { defineStore } from 'pinia';
import tops from '@/utils/tops';
import { commonResponseTransforms } from '@/utils/responseTransformers.js';

export const useJobOffersStore = defineStore('jobOffers', {
  state: () => ({
    jobs: [],
    messages: [],
    selectedJob: null,
    jobDetails: {},
    jobActions: {},
    loading: false,
    error: null
  }),

  getters: {
    getJobDetails: (state) => (jobKey) => state.jobDetails[jobKey],
    getJobActions: (state) => (jobKey) => state.jobActions[jobKey],
    hasError: (state) => Boolean(state.error),
    isLoading: (state) => state.loading
  },

  actions: {
    async fetchNewOffers() {
      this.loading = true;
      this.error = null;

      try {
        const response = await tops.Dispatches.GetNewJobsAndMessages()
          .transformResponse(commonResponseTransforms.jobsAndMessages)
          .send();
        const { jobs, messages } = response.data.Data;

        this.jobs = jobs;
        this.messages = messages;

        return { jobs, messages };
      } catch (error) {
        this.error = error.message || 'Failed to fetch job offers';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async getJobDetails(jobKey) {
      try {
        const response = await tops.Job.GetDetails({ Key: jobKey })
          .transformResponse(commonResponseTransforms.jobDetails)
          .send();
        const details = response.data.Data;

        this.jobDetails[jobKey] = details;

        return details;
      } catch (error) {
        this.error = error.message || 'Failed to fetch job details';
        throw error;
      }
    },

    async getJobActions(jobKey) {
      try {
        const response = await tops.Job.GetPossibleActions({ Key: jobKey })
          .transformResponse(commonResponseTransforms.jobActions)
          .send();
        const actions = response.data.Data;

        this.jobActions[jobKey] = actions;

        return actions;
      } catch (error) {
        this.error = error.message || 'Failed to fetch job actions';
        throw error;
      }
    },

    async handleJobAction(jobKey, responseTag, responseValue) {
      try {
        const response = await tops.Job.HandleAction({
          Key: jobKey,
          ResponseTag: responseTag,
          ResponseValue: responseValue
        });

        return response;
      } catch (error) {
        this.error = error.message || 'Failed to handle job action';
        throw error;
      }
    },

    async acknowledgeMessage(messageKey) {
      try {
        const response = await tops.Message.Acknowledge({ Key: messageKey });

        // Remove message from state after acknowledgment
        this.messages = this.messages.filter(msg => msg.key !== messageKey);

        return response;
      } catch (error) {
        this.error = error.message || 'Failed to acknowledge message';
        throw error;
      }
    },

    selectJob(jobKey) {
      this.selectedJob = jobKey;
    },

    clearError() {
      this.error = null;
    }
  }
});
