import { describe, it, expect, vi, beforeEach } from 'vitest';
import { setActive<PERSON>inia, createP<PERSON> } from 'pinia';
import { useJobOffersStore } from './jobOffers.store';

// Mock the tops utility
vi.mock('@/utils/tops', () => ({
  default: {
    request: vi.fn()
  }
}));

describe('useJobOffersStore', () => {
  let store;
  let pinia;

  beforeEach(() => {
    pinia = createPinia();
    setActivePinia(pinia);
    store = useJobOffersStore();
    vi.clearAllMocks();
  });

  it('should initialize with default state', () => {
    expect(store.$state.jobs).toEqual([]);
    expect(store.$state.messages).toEqual([]);
    expect(store.$state.loading).toBe(false);
    expect(store.$state.error).toBe(null);
  });

  it('should fetch new offers successfully', async () => {
    const mockResponse = {
      data: {
        Jobs: [{ Key: 'job1', Title: 'Test Job', Description: 'Test Description' }],
        Messages: []
      }
    };

    const tops = await import('@/utils/tops');
    tops.default.request.mockResolvedValue(mockResponse);

    await store.fetchNewOffers();

    expect(store.jobs).toHaveLength(1);
    expect(store.jobs[0]).toMatchObject({
      type: 'job',
      key: 'job1',
      title: 'Test Job'
    });
    expect(store.loading).toBe(false);
  });

  it('should handle job action', async () => {
    const tops = await import('@/utils/tops');
    tops.default.request.mockResolvedValue({ data: {} });

    await store.handleJobAction('job1', 'Accept', 'true');

    expect(tops.default.request).toHaveBeenCalledWith('Jobs', 'HandleAction', {
      Key: 'job1',
      ResponseTag: 'Accept',
      ResponseValue: 'true'
    });
  });

  it('should acknowledge message', async () => {
    const tops = await import('@/utils/tops');
    tops.default.request.mockResolvedValue({ data: {} });

    store.messages = [{ key: 'msg1', title: 'Test Message' }];

    await store.acknowledgeMessage('msg1');

    expect(tops.default.request).toHaveBeenCalledWith('Message', 'Acknowledge', { Key: 'msg1' });
    expect(store.messages).toHaveLength(0);
  });

  it('should handle errors', async () => {
    const tops = await import('@/utils/tops');
    tops.default.request.mockRejectedValue(new Error('API Error'));

    await expect(store.fetchNewOffers()).rejects.toThrow('API Error');
    expect(store.error).toBe('API Error');
  });

  it('should select job', () => {
    store.selectJob('job1');
    expect(store.selectedJob).toBe('job1');
  });

  it('should clear error', () => {
    store.error = 'Some error';
    store.clearError();
    expect(store.error).toBe(null);
  });
});
