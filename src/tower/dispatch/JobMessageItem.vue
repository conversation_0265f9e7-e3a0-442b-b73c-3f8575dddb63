<template>
  <div class="job-message-item" @click="handleAcknowledge">
    <div class="message-header">
      <span class="message-title">{{ message.title }}</span>
      <span v-if="message.urgency !== 'normal'" :class="`urgency-${message.urgency}`">
        {{ message.urgency.toUpperCase() }}
      </span>
    </div>
    
    <div class="message-info">
      {{ message.info }}
    </div>
    
    <div class="message-meta">
      <span v-if="message.from" class="message-from">From: {{ message.from }}</span>
      <span v-if="message.expiration" class="message-expiration">
        Expires: {{ message.expiration | verbalDate }}
      </span>
    </div>
    
    <div class="message-actions">
      <el-button 
        size="small" 
        type="primary" 
        @click.stop="handleAcknowledge"
        :loading="acknowledging"
      >
        Acknowledge
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue';

export default {
  name: 'JobMessageItem',
  props: {
    message: {
      type: Object,
      required: true
    }
  },
  emits: ['acknowledge'],
  setup(props, { emit }) {
    const acknowledging = ref(false);

    const handleAcknowledge = async () => {
      if (acknowledging.value) return;
      
      acknowledging.value = true;
      try {
        emit('acknowledge', props.message.key);
      } finally {
        acknowledging.value = false;
      }
    };

    return {
      acknowledging,
      handleAcknowledge
    };
  }
};
</script>

<style scoped>
.job-message-item {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
  background: #fff;
}

.job-message-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-title {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.urgency-high {
  background: #f56c6c;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 600;
}

.urgency-urgent {
  background: #e6a23c;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 600;
}

.message-info {
  color: #606266;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.message-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
  margin-bottom: 12px;
}

.message-actions {
  display: flex;
  justify-content: flex-end;
}
</style>
