<template>
  <div class="job-offer-item" @click="handleSelect">
    <div class="job-header">
      <span class="job-title">{{ job.title }}</span>
      <span v-if="job.priority !== 'normal'" :class="`priority-${job.priority}`">
        {{ job.priority.toUpperCase() }}
      </span>
    </div>
    
    <div class="job-info">
      {{ job.info }}
    </div>
    
    <div class="job-meta">
      <span class="job-status">Status: {{ job.status }}</span>
      <span v-if="job.expiration" class="job-expiration">
        Expires: {{ job.expiration | verbalDate }}
      </span>
    </div>

    <!-- Job Details Panel -->
    <div v-if="isSelected && jobDetails" class="job-details">
      <div class="details-content">
        <h4>Job Details</h4>
        <p><strong>Description:</strong> {{ jobDetails.description }}</p>
        <p v-if="jobDetails.location"><strong>Location:</strong> {{ jobDetails.location }}</p>
        <p v-if="jobDetails.type"><strong>Type:</strong> {{ jobDetails.type }}</p>
        <p v-if="jobDetails.requestedBy"><strong>Requested By:</strong> {{ jobDetails.requestedBy }}</p>
        <div v-if="jobDetails.details" class="job-additional-details">
          <strong>Additional Details:</strong>
          <div v-html="jobDetails.details"></div>
        </div>
      </div>

      <!-- Job Actions -->
      <div v-if="actions && actions.length > 0" class="job-actions">
        <h5>Available Actions</h5>
        <div class="action-buttons">
          <el-button
            v-for="action in actions"
            :key="action.tag"
            :type="getActionButtonType(action.style)"
            size="small"
            @click.stop="handleAction(action)"
            :loading="processingAction === action.tag"
          >
            {{ action.label }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- Confirmation Dialog -->
    <el-dialog
      v-model="showConfirmDialog"
      title="Confirm Action"
      width="400px"
      @close="cancelAction"
    >
      <p>{{ confirmMessage }}</p>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelAction">Cancel</el-button>
          <el-button type="primary" @click="executeAction" :loading="processingAction">
            Confirm
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue';

export default {
  name: 'JobOfferItem',
  props: {
    job: {
      type: Object,
      required: true
    },
    selectedJobKey: {
      type: String,
      default: null
    }
  },
  emits: ['select', 'action'],
  setup(props, { emit }) {
    const jobDetails = ref(null);
    const actions = ref([]);
    const loadingDetails = ref(false);
    const loadingActions = ref(false);
    const processingAction = ref(null);
    const showConfirmDialog = ref(false);
    const confirmMessage = ref('');
    const pendingAction = ref(null);

    const isSelected = computed(() => props.selectedJobKey === props.job.key);

    const handleSelect = async () => {
      if (isSelected.value) return;
      
      emit('select', props.job.key);
    };

    const loadJobDetails = async () => {
      if (loadingDetails.value || jobDetails.value) return;
      
      loadingDetails.value = true;
      try {
        emit('get-details', props.job.key);
      } catch (error) {
        console.error('Failed to load job details:', error);
      } finally {
        loadingDetails.value = false;
      }
    };

    const loadJobActions = async () => {
      if (loadingActions.value || actions.value.length > 0) return;
      
      loadingActions.value = true;
      try {
        emit('get-actions', props.job.key);
      } catch (error) {
        console.error('Failed to load job actions:', error);
      } finally {
        loadingActions.value = false;
      }
    };

    const handleAction = (action) => {
      if (action.requiresConfirmation && action.confirmMessage) {
        pendingAction.value = action;
        confirmMessage.value = action.confirmMessage;
        showConfirmDialog.value = true;
      } else {
        executeActionDirect(action);
      }
    };

    const executeAction = () => {
      if (pendingAction.value) {
        executeActionDirect(pendingAction.value);
      }
      cancelAction();
    };

    const executeActionDirect = async (action) => {
      processingAction.value = action.tag;
      try {
        emit('action', {
          jobKey: props.job.key,
          responseTag: action.tag,
          responseValue: action.value
        });
      } catch (error) {
        console.error('Failed to execute action:', error);
      } finally {
        processingAction.value = null;
      }
    };

    const cancelAction = () => {
      showConfirmDialog.value = false;
      confirmMessage.value = '';
      pendingAction.value = null;
    };

    const getActionButtonType = (style) => {
      const typeMap = {
        primary: 'primary',
        success: 'success',
        warning: 'warning',
        danger: 'danger',
        default: 'default'
      };
      return typeMap[style] || 'default';
    };

    // Watch for selection changes
    watch(isSelected, (selected) => {
      if (selected) {
        loadJobDetails();
        loadJobActions();
      }
    });

    return {
      jobDetails,
      actions,
      loadingDetails,
      loadingActions,
      processingAction,
      showConfirmDialog,
      confirmMessage,
      isSelected,
      handleSelect,
      handleAction,
      executeAction,
      cancelAction,
      getActionButtonType
    };
  }
};
</script>

<style scoped>
.job-offer-item {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
  background: #fff;
}

.job-offer-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.job-offer-item.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.job-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.job-title {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.priority-high {
  background: #f56c6c;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 600;
}

.priority-urgent {
  background: #e6a23c;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 600;
}

.job-info {
  color: #606266;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.job-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
  margin-bottom: 12px;
}

.job-details {
  border-top: 1px solid #ebeef5;
  margin-top: 16px;
  padding-top: 16px;
}

.details-content h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
}

.details-content p {
  margin: 8px 0;
  font-size: 13px;
  color: #606266;
}

.job-additional-details {
  margin-top: 12px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
}

.job-actions {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #ebeef5;
}

.job-actions h5 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 13px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.dialog-footer {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}
</style>
