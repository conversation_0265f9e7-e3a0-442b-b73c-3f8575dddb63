<template>
  <div class="job-offers">
    <div class="job-offers-header">
      <h3>Offers & Messages</h3>
      <div class="header-actions">
        <app-button
          size="small"
          @click="refreshOffers"
          :loading="loading"
          icon="el-icon-refresh">
          Refresh
        </app-button>
        <app-button
          size="small"
          :type="isPolling ? 'danger' : 'success'"
          @click="togglePolling">
          {{ isPolling ? 'Stop Auto-Refresh' : 'Start Auto-Refresh' }}
        </app-button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading && !jobs.length && !messages.length" class="loading-state">
      <el-spinner />
      <span>Loading job offers...</span>
    </div>

    <!-- Empty State -->
    <div v-else-if="!loading && !jobs.length && !messages.length" class="empty-state">
      <el-empty description="No job offers or messages available" />
    </div>

    <!-- Content -->
    <div v-else class="job-offers-content">
      <!-- Job Offers Section -->
      <div v-if="jobs.length > 0" class="jobs-section">
        <h4 class="section-title">
          Job Offers ({{ jobs.length }})
        </h4>
        <div class="jobs-list">
          <JobOfferItem
            v-for="job in jobs"
            :key="job.key"
            :job="job"
            :selected-job-key="selectedJob"
            @select="handleJobSelect"
            @get-details="handleGetJobDetails"
            @get-actions="handleGetJobActions"
            @action="handleJobActionForTemplate"
          />
        </div>
      </div>

      <!-- Messages Section -->
      <div v-if="messages.length > 0" class="messages-section">
        <h4 class="section-title">
          Messages ({{ messages.length }})
        </h4>
        <div class="messages-list">
          <JobMessageItem
            v-for="message in messages"
            :key="message.key"
            :message="message"
            @acknowledge="handleMessageAcknowledge"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue';
import { useJobOffers } from './useJobOffers';
import JobOfferItem from './JobOfferItem.vue';
import JobMessageItem from './JobMessageItem.vue';

const props = defineProps({
  pollInterval: {
    type: Number,
    default: 30000 // 30 seconds
  },
  autoStart: {
    type: Boolean,
    default: true
  }
});

// Convert props to refs to maintain reactivity
const { pollInterval, autoStart } = toRefs(props);

const {
  jobs,
  messages,
  selectedJob,
  loading,
  error,
  isPolling,
  loadJobs,
  getJobDetails,
  getJobActions,
  handleJobAction,
  acknowledgeMessage,
  selectJob,
  clearError,
  startPolling,
  stopPolling
} = useJobOffers({
  pollInterval,
  autoStart
});

const refreshOffers = async () => {
  try {
    await loadJobs();
  } catch (error) {
    console.error('Failed to refresh offers:', error);
  }
};

const togglePolling = () => {
  if (isPolling.value) {
    stopPolling();
  } else {
    startPolling();
  }
};

const handleJobSelect = (jobKey) => {
  selectJob(jobKey);
};

const handleGetJobDetails = async (jobKey) => {
  try {
    await getJobDetails(jobKey);
  } catch (error) {
    console.error('Failed to get job details:', error);
  }
};

const handleGetJobActions = async (jobKey) => {
  try {
    await getJobActions(jobKey);
  } catch (error) {
    console.error('Failed to get job actions:', error);
  }
};

const handleJobActionExecute = async ({ jobKey, responseTag, responseValue }) => {
  try {
    await handleJobAction(jobKey, responseTag, responseValue);
  } catch (error) {
    console.error('Failed to execute job action:', error);
  }
};

const handleMessageAcknowledge = async (messageKey) => {
  try {
    await acknowledgeMessage(messageKey);
  } catch (error) {
    console.error('Failed to acknowledge message:', error);
  }
};

const totalItems = computed(() => jobs.value.length + messages.value.length);

// Expose handleJobActionExecute as handleJobAction for template
const handleJobActionForTemplate = handleJobActionExecute;
</script>

<style scoped>
.job-offers {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.job-offers-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.job-offers-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #909399;
}

.loading-state span {
  margin-top: 12px;
  font-size: 14px;
}

.empty-state {
  padding: 40px;
}

.job-offers-content {
  max-height: 600px;
  overflow-y: auto;
}

.jobs-section,
.messages-section {
  margin-bottom: 24px;
}

.section-title {
  margin: 0 0 16px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.jobs-list,
.messages-list {
  display: flex;
  flex-direction: column;
}

/* Responsive Design */
@media (max-width: 768px) {
  .job-offers {
    padding: 16px;
  }

  .job-offers-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .job-offers-content {
    max-height: 500px;
  }
}
</style>
