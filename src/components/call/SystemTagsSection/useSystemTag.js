import tops from '@/utils/tops';
import { commonResponseTransforms } from '@/utils/responseTransformers.js';

export default function useSystemTag (options) {
  const getAvailableSystemTags = async () => {
    try {
      const response = await tops.TOPSCompany.GetSystemDataTags()
        .transformResponse(commonResponseTransforms.systemTags)
        .send();
      return response.data.Data;
    } catch {
      return [];
    }
  };

  return {
    getAvailableSystemTags
  };
}
