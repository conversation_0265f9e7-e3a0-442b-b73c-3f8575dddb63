import { describe, it, expect, vi, beforeEach } from 'vitest';
import axios from 'axios';
import tops from '../tops';
import { applyRequestTransformSchema } from '../requestTransformers';
import { applyResponseTransformSchema, transformers } from '../responseTransformers';

// Mock axios
vi.mock('axios');
const mockedAxios = vi.mocked(axios);

// Mock store
vi.mock('@/store', () => ({
  default: {
    state: {
      product: { key: 'TEST' },
      orgUnitKey: 1,
      appMode: 'TEST',
      user: { Key: 123 },
      instance: { Key: 456, Authentication: 'test-auth' }
    },
    getters: {
      __state: { appMode: 'TEST' }
    }
  }
}));

// Mock filters
vi.mock('@/utils/filters', () => ({
  storifyDate: vi.fn(date => date || '')
}));

describe('Fluent TOPS API', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockedAxios.post.mockResolvedValue({
      data: { Result: 'Success', Data: { Key: '123', Active: 'true' } }
    });
  });

  describe('Basic Fluent Interface', () => {
    it('should create a fluent request builder', () => {
      const request = tops.Call.Read({ lCallKey: 123 });

      expect(request).toHaveProperty('send');
      expect(request).toHaveProperty('transformRequest');
      expect(request).toHaveProperty('transformResponse');
      expect(request).toHaveProperty('asCallData');
      expect(request).toHaveProperty('expectCallData');
    });

    it('should be thenable for backward compatibility', async () => {
      const response = await tops.Call.Read({ lCallKey: 123 });

      expect(mockedAxios.post).toHaveBeenCalledOnce();
      expect(response.data).toEqual({ Result: 'Success', Data: { Key: '123', Active: 'true' } });
    });

    it('should execute request with send()', async () => {
      const response = await tops.Call.Read({ lCallKey: 123 }).send();

      expect(mockedAxios.post).toHaveBeenCalledOnce();
      expect(response.data).toEqual({ Result: 'Success', Data: { Key: '123', Active: 'true' } });
    });
  });

  describe('Request Transformations', () => {
    it('should apply request transformations', async () => {
      const callData = {
        lCallKey: 123,
        Active: true,
        TaxRate: 8.25
      };

      await tops.Call.Update(callData)
        .transformRequest({
          Active: 'booleanToString',
          TaxRate: 'numberToString'
        })
        .send();

      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          Data: expect.objectContaining({
            lCallKey: 123,
            Active: '1',
            TaxRate: '8.25'
          })
        }),
        expect.any(Object)
      );
    });

    it('should use pre-built call data transformations', async () => {
      const callData = {
        lCallKey: 123,
        Active: true,
        vc255DelimitedInfo: 'tag1;tag2;tag3'
      };

      await tops.Call.Update(callData)
        .asCallData()
        .send();

      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          Data: expect.objectContaining({
            lCallKey: 123,
            Active: '1',
            vc255DelimitedInfo: 'tag1;tag2;tag3;'
          })
        }),
        expect.any(Object)
      );
    });
  });

  describe('Response Transformations', () => {
    it('should apply response transformations', async () => {
      mockedAxios.post.mockResolvedValue({
        data: {
          Result: 'Success',
          Data: { Key: '123', Active: 'true', TaxRate: '8.25' }
        }
      });

      const response = await tops.Call.Read({ lCallKey: 123 })
        .transformResponse({
          Data: {
            Key: 'number',
            Active: 'boolean',
            TaxRate: 'number'
          }
        })
        .send();

      expect(response.data.Data).toEqual({
        Key: 123,
        Active: true,
        TaxRate: 8.25
      });
    });

    it('should use pre-built response transformations', async () => {
      mockedAxios.post.mockResolvedValue({
        data: {
          Result: 'Success',
          Data: [
            { Key: '123', Active: 'true' },
            { Key: '456', Active: 'false' }
          ]
        }
      });

      const response = await tops.TOPSCompany.GetEmployees({})
        .expectEmployeeList()
        .send();

      expect(response.data.Data).toEqual([
        { Key: 123, Active: true },
        { Key: 456, Active: false }
      ]);
    });
  });

  describe('Combined Transformations', () => {
    it('should apply both request and response transformations', async () => {
      const callData = {
        lCallKey: 123,
        Active: true,
        TaxRate: 8.25
      };

      mockedAxios.post.mockResolvedValue({
        data: {
          Result: 'Success',
          Data: { Key: '123', Active: '1', TaxRate: '8.25' }
        }
      });

      const response = await tops.Call.Update(callData)
        .transformRequest({
          Active: 'booleanToString',
          TaxRate: 'numberToString'
        })
        .transformResponse({
          Data: {
            Key: 'number',
            Active: 'boolean',
            TaxRate: 'number'
          }
        })
        .send();

      // Check request transformation
      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          Data: expect.objectContaining({
            Active: '1',
            TaxRate: '8.25'
          })
        }),
        expect.any(Object)
      );

      // Check response transformation
      expect(response.data.Data).toEqual({
        Key: 123,
        Active: true,
        TaxRate: 8.25
      });
    });

    it('should use transform() method for both request and response', async () => {
      const callData = { lCallKey: 123, Active: true };

      mockedAxios.post.mockResolvedValue({
        data: { Result: 'Success', Data: { Key: '123', Active: '1' } }
      });

      const response = await tops.Call.Update(callData)
        .transform(
          { Active: 'booleanToString' },  // Request transform
          { Data: { Key: 'number', Active: 'boolean' } }  // Response transform
        )
        .send();

      expect(response.data.Data).toEqual({
        Key: 123,
        Active: true
      });
    });
  });

  describe('Pre-built Shortcuts', () => {
    it('should chain multiple shortcuts', async () => {
      const callData = {
        lCallKey: 123,
        Active: true,
        TaxRate: 8.25
      };

      mockedAxios.post.mockResolvedValue({
        data: {
          Result: 'Success',
          Data: { Key: '123', Active: '1', TaxRate: '8.25' }
        }
      });

      const response = await tops.Call.Update(callData)
        .asCallData()      // Request transformation
        .expectCallData()  // Response transformation
        .send();

      expect(mockedAxios.post).toHaveBeenCalled();
      expect(response.data).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      const apiError = new Error('API Error');
      mockedAxios.post.mockRejectedValue(apiError);

      await expect(
        tops.Call.Read({ lCallKey: 123 }).send()
      ).rejects.toThrow('API Error');
    });

    it('should handle transformation errors gracefully', async () => {
      // This should not throw during transformation setup
      const request = tops.Call.Update({ lCallKey: 123 })
        .transformRequest({ InvalidField: 'invalidTransform' });

      expect(request).toBeDefined();
    });
  });
});
