import axios from 'axios';
import store from '@/store';
import { VERSION } from '@/config';
import { storifyDate } from '@/utils/filters';
import { applyResponseTransformSchema, commonResponseTransforms } from '@/utils/responseTransformers';
import { applyRequestTransformSchema, commonRequestTransforms } from '@/utils/requestTransformers';

class ApiClient {
  apiUrl = import.meta.env.VITE_TXI_API;
  timeout = 60_000;
  debug = store?.getters.__state?.appMode === 'DEBUG';
  pendingRequests = new Map();

  /**
   * Make a request to the TOPS API
   */
  async request(noun, verb, data = {}, options = {}) {
    const params = this._buildParameters(noun, verb, data, options);
    const requestKey = this._createRequestKey(noun, verb, data, options);

    // Optionally cancel previous request
    if (options.cancelPrevious) {
      const existing = this.pendingRequests.get(requestKey);
      if (existing) {
        existing.controller.abort();
        if (this.debug) {
          console.log(`Cancelled previous request for ${noun}.${verb}`);
        }
        this.pendingRequests.delete(requestKey);
      }
    }

    // Return existing pending request if present
    if (this.pendingRequests.has(requestKey)) {
      if (this.debug) {
        console.log(`Duplicate request for ${noun}.${verb}, returning existing promise`);
      }
      return this.pendingRequests.get(requestKey).promise;
    }

    // Create new controller and signal
    const controller = new AbortController();
    const signal = options.signal || controller.signal;

    // Initiate request and clean up when done
    const requestPromise = this._makeHttpRequest(params, noun, verb, signal)
      .finally(() => {
        this.pendingRequests.delete(requestKey);
      });

    this.pendingRequests.set(requestKey, { promise: requestPromise, controller });
    return requestPromise;
  }

  /**
   * Make the actual HTTP request
   */
  async _makeHttpRequest(params, noun, verb, signal) {
    try {
      const response = await axios.post(this.apiUrl, params, {
        headers: {
          'X-txi-api': `App:TOPS Browser,Version:${VERSION},Noun:${noun},Verb:${verb}`
        },
        timeout: this.timeout,
        signal
      });

      return response;
    } catch (error) {
      // Handle cancellation gracefully
      if (axios.isCancel(error) || error.code === 'ERR_CANCELED') {
        const cancelError = new Error(`Request cancelled: ${noun}.${verb}`);
        cancelError.name = 'RequestCancelled';
        cancelError.originalError = error;
        throw cancelError;
      }

      // Log error in debug mode
      if (this.debug) {
        console.log(`%cerror → %c${noun}, ${verb})`,
          'font-variant: small-caps; color: #FF4136',
          'font-weight: bold',
          'color: #AAAAAA',
          error);
      }

      throw error;
    }
  }

  /**
   * Create a unique key for request deduplication
   */
  _createRequestKey(noun, verb, data = {}, options = {}) {
    const keyData = {
      noun,
      verb,
      data,
      lastRead: options.lastRead
    };

    return JSON.stringify(keyData);
  }

  /**
   * Build request parameters
   */
  _buildParameters(noun, verb, data = {}, options = {}) {
    const { product, orgUnitKey, appMode: mode, user, instance } = store.state;

    return {
      Operation: {
        Noun: noun,
        Verb: verb,
        ProductKey: product.key,
        OrgUnitKey: orgUnitKey,
        Mode: mode,
        ResponseData: 'JSON',
        LastRead: storifyDate(options.lastRead ?? '')
      },
      Authentication: {
        UserKey: user.Key,
        InstanceKey: instance.Key,
        AuthenticationKey: instance.Authentication
      },
      Data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        ...data
      }
    };
  }
}

// Create a singleton API client
const apiClient = new ApiClient();

/**
 * Fluent API request builder
 */
class FluentRequest {
  constructor(noun, verb, data = {}, options = {}) {
    this.noun = noun;
    this.verb = verb;
    this.data = { ...data };
    this.options = { ...options };
    this.requestTransformSchema = null;
    this.responseTransformSchema = null;
  }

  /**
   * Apply request transformation schema
   */
  transformRequest(schema) {
    this.requestTransformSchema = schema;
    return this;
  }

  /**
   * Apply response transformation schema
   */
  transformResponse(schema) {
    this.responseTransformSchema = schema;
    return this;
  }

  /**
   * Apply both request and response transformations
   */
  transform(requestSchema, responseSchema = null) {
    if (requestSchema) this.requestTransformSchema = requestSchema;
    if (responseSchema) this.responseTransformSchema = responseSchema;
    return this;
  }

  /**
   * Pre-built transform shortcuts for common patterns
   */
  asCallData() {
    return this.transformRequest(commonRequestTransforms.callData);
  }

  asCustomerData() {
    return this.transformRequest(commonRequestTransforms.customerData);
  }

  asEmployeeData() {
    return this.transformRequest(commonRequestTransforms.employeeData);
  }

  asPricingData() {
    return this.transformRequest(commonRequestTransforms.pricingData);
  }

  asSystemTagData() {
    return this.transformRequest(commonRequestTransforms.systemTagData);
  }

  /**
   * Pre-built response transform shortcuts
   */
  expectEmployeeList() {
    return this.transformResponse(commonResponseTransforms.employeeList);
  }

  expectStatusList() {
    return this.transformResponse(commonResponseTransforms.statusList);
  }

  expectReportList() {
    return this.transformResponse(commonResponseTransforms.reportList);
  }

  expectInspectionItems() {
    return this.transformResponse(commonResponseTransforms.inspectionItems);
  }

  expectSystemTags() {
    return this.transformResponse(commonResponseTransforms.systemTags);
  }

  expectLocationData() {
    return this.transformResponse(commonResponseTransforms.locationData);
  }

  expectCallData() {
    return this.transformResponse(commonResponseTransforms.callData);
  }

  expectCustomerData() {
    return this.transformResponse(commonResponseTransforms.customerData);
  }

  expectPricingData() {
    return this.transformResponse(commonResponseTransforms.pricingData);
  }

  /**
   * Execute the request
   */
  async send() {
    let requestData = this.data;

    // Apply request transformation if specified
    if (this.requestTransformSchema) {
      requestData = applyRequestTransformSchema(this.data, this.requestTransformSchema);
    }

    // Make the API request
    const response = await apiClient.request(this.noun, this.verb, requestData, this.options);

    // Apply response transformation if specified
    if (this.responseTransformSchema && response.data) {
      const transformedData = applyResponseTransformSchema(response.data, this.responseTransformSchema);
      return {
        ...response,
        data: transformedData
      };
    }

    return response;
  }

  /**
   * Alias for send() for backward compatibility
   */
  async execute() {
    return this.send();
  }
}

/**
 * Create a noun-specific API namespace with fluent interface
 */
function createNounApi(noun) {
  return new Proxy({}, {
    get(_, verb) {
      // Return a function that creates a FluentRequest
      return (data = {}, options = {}) => {
        // If no transformations are needed, return the old simple interface
        if (typeof data === 'object' && !Array.isArray(data) &&
            !options.requestTransform && !options.responseTransform) {

          // Create a FluentRequest but also add the old direct call capability
          const fluentRequest = new FluentRequest(noun, verb, data, options);

          // Make it thenable for backward compatibility
          fluentRequest.then = function(onFulfilled, onRejected) {
            return this.send().then(onFulfilled, onRejected);
          };

          fluentRequest.catch = function(onRejected) {
            return this.send().catch(onRejected);
          };

          return fluentRequest;
        }

        // For explicit options, create FluentRequest
        return new FluentRequest(noun, verb, data, options);
      };
    }
  });
}

/**
 * Main export
 */
const tops = new Proxy({}, {
  get(target, noun) {
    // Create and cache the noun API if it doesn't exist
    if (!target[noun]) {
      target[noun] = createNounApi(noun);
    }
    return target[noun];
  }
});

export default tops;
