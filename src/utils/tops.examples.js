/**
 * Examples of using the new fluent TOPS API with request and response transformers
 */

import tops from '@/utils/tops';
import { requestTransformers, commonRequestTransforms } from '@/utils/requestTransformers';
import { commonResponseTransforms } from '@/utils/responseTransformers';

// ============================================================================
// BASIC USAGE EXAMPLES
// ============================================================================

export async function basicExamples() {
  // Simple request (backward compatible)
  const callResponse = await tops.Call.Read({ lCallKey: 12345 });

  // Fluent API with request transformation
  const updateResponse = await tops.Call.Update({
    lCallKey: 12345,
    Active: true,
    TaxRate: 8.25,
    vc255DelimitedInfo: 'tag1;tag2;tag3'
  })
  .asCallData()  // Transforms: Active -> '1', TaxRate -> '8.25', ensures trailing ';'
  .send();

  // Fluent API with response transformation
  const employeesResponse = await tops.TOPSCompany.GetEmployees({})
    .expectEmployeeList()  // Transforms Key to number, Active to boolean
    .send();

  // Combined request and response transformations
  const customerResponse = await tops.Customer.Update({
    lCustomerKey: 456,
    Active: true,
    TaxExempt: false,
    CreditLimit: 5000.00
  })
  .asCustomerData()      // Transform outgoing data
  .expectCustomerData()  // Transform incoming response
  .send();

  return { callResponse, updateResponse, employeesResponse, customerResponse };
}

// ============================================================================
// ADVANCED TRANSFORMATION EXAMPLES
// ============================================================================

export async function advancedExamples() {
  // Custom request transformations
  const customResponse = await tops.SystemTag.Create({
    Name: 'New Tag',
    Active: true,
    Modifiable: false,
    SortOrder: 10,
    TagValues: ['value1', 'value2', 'value3']
  })
  .transformRequest({
    Active: 'booleanToString',
    Modifiable: 'booleanToString',
    SortOrder: 'numberToString',
    TagValues: requestTransformers.arrayToDelimited(';')
  })
  .transformResponse({
    Data: {
      Key: 'number',
      Active: 'boolean',
      Modifiable: 'boolean'
    }
  })
  .send();

  // Conditional transformations
  const conditionalResponse = await tops.Call.Update({
    lCallKey: 789,
    CustomerKey: 0,  // Will be converted to null
    Active: 'true',  // Will be converted to '1' only if it's a boolean
    Notes: null      // Will be converted to empty string
  })
  .transformRequest({
    CustomerKey: requestTransformers.when(
      val => val === 0,
      () => null
    ),
    Active: requestTransformers.when(
      val => typeof val === 'boolean',
      requestTransformers.booleanToString
    ),
    Notes: 'nullToEmpty'
  })
  .send();

  // Array transformations
  const arrayResponse = await tops.Inspection.UpdateItems({
    Items: [
      { Key: 1, Active: true, Required: false, Order: 1 },
      { Key: 2, Active: false, Required: true, Order: 2 }
    ]
  })
  .transformRequest({
    Items: requestTransformers.transformArray({
      Key: 'numberToString',
      Active: 'booleanToString',
      Required: 'booleanToString',
      Order: 'numberToString'
    })
  })
  .send();

  return { customResponse, conditionalResponse, arrayResponse };
}

// ============================================================================
// REAL-WORLD USAGE PATTERNS
// ============================================================================

export async function realWorldExamples() {
  // Call management with pricing
  const callWithPricing = await tops.Call.Update({
    lCallKey: 12345,
    Active: true,
    Retow: false,
    TaxRate: 8.25,
    DiscountPct: 10.0,
    Total: 150.00,
    vc255DelimitedInfo: 'priority;urgent;customer_vip'
  })
  .transform(
    // Request transformations
    {
      ...commonRequestTransforms.callData,
      ...commonRequestTransforms.pricingData
    },
    // Response transformations
    {
      ...commonResponseTransforms.callData,
      ...commonResponseTransforms.pricingData
    }
  )
  .send();

  // Employee list with filtering
  const activeEmployees = await tops.TOPSCompany.GetEmployees({
    ShowInactive: false,
    DepartmentKey: 5
  })
  .transformRequest({
    ShowInactive: 'booleanToString',
    DepartmentKey: 'numberToString'
  })
  .expectEmployeeList()
  .send();

  // System configuration update
  const configUpdate = await tops.SystemConfig.Update({
    Settings: {
      AutoBackup: true,
      BackupInterval: 24,
      MaxRetries: 3,
      EnableLogging: false
    },
    ModifiedBy: 'admin',
    ModifiedDate: new Date()
  })
  .transformRequest({
    'Settings.AutoBackup': 'booleanToString',
    'Settings.BackupInterval': 'numberToString',
    'Settings.MaxRetries': 'numberToString',
    'Settings.EnableLogging': 'booleanToString',
    ModifiedDate: 'datetimeToTops'
  })
  .send();

  return { callWithPricing, activeEmployees, configUpdate };
}

// ============================================================================
// ERROR HANDLING EXAMPLES
// ============================================================================

export async function errorHandlingExamples() {
  try {
    // Fluent API with error handling
    const response = await tops.Call.Update({
      lCallKey: 99999,  // Non-existent call
      Active: true
    })
    .asCallData()
    .expectCallData()
    .send();

    return response;
  } catch (error) {
    if (error.name === 'RequestCancelled') {
      console.log('Request was cancelled');
    } else {
      console.error('API call failed:', error.message);
    }
    throw error;
  }
}

// ============================================================================
// MIGRATION EXAMPLES
// ============================================================================

export function migrationExamples() {
  // Before: Old Requester pattern
  /*
  new StandardRequest(context, {
    noun: 'Call',
    verb: 'Update',
    data: {
      lCallKey: 12345,
      Active: true,
      TaxRate: 8.25
    }
  })
  .requestMiddleware(data => {
    data.Active = data.Active ? '1' : '0';
    data.TaxRate = String(data.TaxRate);
  })
  .responseMiddleware(response => {
    if (response.data && response.data.Data) {
      response.data.Data.Key = Number(response.data.Data.Key);
      response.data.Data.Active = response.data.Data.Active === '1';
    }
  })
  .success(callback);
  */

  // After: Fluent API
  const migratedCall = async () => {
    return await tops.Call.Update({
      lCallKey: 12345,
      Active: true,
      TaxRate: 8.25
    })
    .asCallData()      // Handles request transformations
    .expectCallData()  // Handles response transformations
    .send();
  };

  return migratedCall;
}

// ============================================================================
// CUSTOM PRESET EXAMPLES
// ============================================================================

// Define custom transform combinations
export const customTransforms = {
  // Special call data with additional fields
  enhancedCallData: {
    ...commonRequestTransforms.callData,
    Priority: 'numberToString',
    EstimatedDuration: 'numberToString',
    SpecialInstructions: requestTransformers.arrayToDelimited('|'),
    ScheduledDate: 'dateToTops'
  },

  // Customer with contact information
  customerWithContacts: {
    ...commonRequestTransforms.customerData,
    PrimaryPhone: 'string',
    SecondaryPhone: 'string',
    EmailAddresses: requestTransformers.arrayToDelimited(';'),
    PreferredContactMethod: 'numberToString'
  }
};

export async function customPresetExamples() {
  // Use custom presets
  const enhancedCall = await tops.Call.Create({
    CustomerKey: 123,
    Active: true,
    Priority: 1,
    EstimatedDuration: 120,
    SpecialInstructions: ['Handle with care', 'Customer VIP', 'Rush job'],
    ScheduledDate: new Date('2024-01-15')
  })
  .transformRequest(customTransforms.enhancedCallData)
  .send();

  const customerWithContacts = await tops.Customer.Update({
    lCustomerKey: 456,
    Active: true,
    TaxExempt: false,
    PrimaryPhone: '555-1234',
    SecondaryPhone: '555-5678',
    EmailAddresses: ['<EMAIL>', '<EMAIL>'],
    PreferredContactMethod: 1
  })
  .transformRequest(customTransforms.customerWithContacts)
  .send();

  return { enhancedCall, customerWithContacts };
}
