# TOPS API Request Transformation System

This document describes the request transformation system for outgoing TOPS API calls, designed to complement the response transformation system with a unified fluent API pattern.

## Overview

The TOPS API often expects data in specific formats (strings instead of booleans, delimited strings, etc.). The request transformation system provides clean, reusable ways to transform outgoing JavaScript data to match TOPS API expectations.

## Fluent API Pattern

The new fluent API allows chaining both request and response transformations:

### Basic Usage

```javascript
// Simple request (backward compatible)
const response = await tops.Call.Read({ lCallKey: 12345 });

// Fluent API with transformations
const response = await tops.Call.Update(callData)
  .transformRequest({ Active: 'booleanToString' })
  .transformResponse(commonResponseTransforms.callData)
  .send();
```

### Pre-built Transform Shortcuts

```javascript
// Request transformations
await tops.Call.Update(callData)
  .asCallData()  // Applies common call data transformations
  .send();

await tops.Customer.Create(customerData)
  .asCustomerData()  // Applies common customer transformations
  .send();

// Response transformations
const response = await tops.TOPSCompany.GetEmployees({})
  .expectEmployeeList()  // Applies employee list response transforms
  .send();

// Combined transformations
const response = await tops.Call.Update(callData)
  .asCallData()           // Transform outgoing data
  .expectCallData()       // Transform incoming response
  .send();
```

### Advanced Chaining

```javascript
// Custom transformations
const response = await tops.SystemTag.GetList({})
  .transformRequest({
    Active: 'booleanToString',
    SortOrder: 'numberToString'
  })
  .transformResponse({
    Data: transformers.transformArray({
      Modifiable: 'boolean',
      Active: 'boolean'
    })
  })
  .send();

// Conditional transformations
const response = await tops.Call.Update(callData)
  .transformRequest({
    vc255DelimitedInfo: requestTransformers.ensureDelimitedFormat(';'),
    Active: requestTransformers.when(
      val => typeof val === 'boolean',
      requestTransformers.booleanToString
    )
  })
  .send();
```

## Request Transformation Types

### Built-in Transform Types

- `'string'` - Convert to string
- `'booleanToString'` - Convert boolean to '1'/'0'
- `'numberToString'` - Convert number to string
- `'nullToEmpty'` - Convert null/undefined to empty string
- `'dateToTops'` - Convert Date to TOPS date format
- `'datetimeToTops'` - Convert Date to TOPS datetime format

### Custom Transformers

```javascript
import { requestTransformers } from '@/utils/requestTransformers';

// Array to delimited string
const response = await tops.Call.Update({
  Tags: ['tag1', 'tag2', 'tag3']
})
.transformRequest({
  Tags: requestTransformers.arrayToDelimited(';')
})
.send();
// Tags becomes: "tag1;tag2;tag3;"

// Conditional transformation
const response = await tops.Call.Update(callData)
.transformRequest({
  CustomerKey: requestTransformers.when(
    val => val === 0,
    () => null
  )
})
.send();
```

## Pre-built Common Transforms

### Request Transforms

```javascript
import { commonRequestTransforms } from '@/utils/requestTransformers';

// Available presets:
commonRequestTransforms.callData        // Call-related fields
commonRequestTransforms.customerData    // Customer-related fields
commonRequestTransforms.employeeData    // Employee-related fields
commonRequestTransforms.pricingData     // Pricing/financial fields
commonRequestTransforms.systemTagData   // System tag fields
```

### Response Transforms (from existing system)

```javascript
// Available via fluent shortcuts:
.expectEmployeeList()     // Employee/driver/truck lists
.expectStatusList()       // Status lists
.expectReportList()       // Report lists with boolean flags
.expectInspectionItems()  // Inspection items with nested arrays
.expectSystemTags()       // System tags with boolean conversion
.expectLocationData()     // Location data with coordinates
```

## Backward Compatibility

The fluent API maintains full backward compatibility:

```javascript
// Old way still works
const response = await tops.Call.Read({ lCallKey: 12345 });

// New way provides more power
const response = await tops.Call.Read({ lCallKey: 12345 })
  .expectCallData()
  .send();

// Thenable interface for gradual migration
tops.Call.Read({ lCallKey: 12345 })
  .then(response => {
    // Handle response
  });
```

## Migration Examples

### From Old Request Middleware

```javascript
// Before (with old Requester)
new StandardRequest(context, {
  noun: 'Call',
  verb: 'Update',
  data: callData
})
.requestMiddleware(data => {
  data.Active = data.Active ? '1' : '0';
  data.vc255DelimitedInfo = data.vc255DelimitedInfo + ';';
})
.success(callback);

// After (with fluent API)
const response = await tops.Call.Update(callData)
  .asCallData()  // Handles Active and vc255DelimitedInfo automatically
  .send();
```

### From Manual Response Processing

```javascript
// Before
const response = await tops.TOPSCompany.GetEmployees({});
const employees = response.data.Data.map(emp => ({
  ...emp,
  Key: Number(emp.Key),
  Active: emp.Active === 'true'
}));

// After
const response = await tops.TOPSCompany.GetEmployees({})
  .expectEmployeeList()
  .send();
const employees = response.data.Data; // Already transformed
```

## Creating Custom Presets

```javascript
// Define custom transform combinations
const myCustomTransforms = {
  specialCallData: {
    ...commonRequestTransforms.callData,
    SpecialField: 'numberToString',
    CustomArray: requestTransformers.arrayToDelimited('|')
  }
};

// Use in fluent API
const response = await tops.Call.Update(callData)
  .transformRequest(myCustomTransforms.specialCallData)
  .send();
```

## Error Handling

```javascript
try {
  const response = await tops.Call.Update(callData)
    .asCallData()
    .expectCallData()
    .send();

  // Handle success
} catch (error) {
  // Handle error
  console.error('API call failed:', error);
}
```

## Best Practices

1. **Use presets when possible** - `asCallData()` instead of manual transforms
2. **Chain transformations** - Combine request and response transforms in one call
3. **Gradual migration** - The old API still works, migrate incrementally
4. **Custom presets** - Create reusable transform combinations for your specific needs
5. **Error handling** - Always wrap fluent API calls in try/catch blocks
