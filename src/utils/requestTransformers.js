/**
 * Request transformation schemas for outgoing TOPS API data
 */
export const requestTransformSchemas = {
  // Convert JavaScript types to TOPS API expected formats
  stringFields: {
    Description: 'string',
    Notes: 'string',
    Address: 'string',
    Name: 'string',
    Title: 'string'
  },
  
  // Convert booleans to string representations
  booleanToStringFields: {
    Active: 'booleanToString',
    Required: 'booleanToString',
    IsVisible: 'booleanToString',
    Retow: 'booleanToString',
    ReleasePaymentRequired: 'booleanToString',
    AskToReleaseHolds: 'booleanToString',
    AskToTerminateLien: 'booleanToString',
    AskToRemoveLienPricing: 'booleanToString'
  },
  
  // Convert numbers to strings (if TOPS expects strings)
  numericToStringFields: {
    TaxRate: 'numberToString',
    TaxRateOverride: 'numberToString',
    DiscountPct: 'numberToString',
    Total: 'numberToString',
    TaxTotal: 'numberToString',
    DiscountTotal: 'numberToString',
    TowBalance: 'numberToString',
    SaleTaxRate: 'numberToString'
  },
  
  // Handle null/undefined values
  nullableFields: {
    CustomerKey: 'nullToEmpty',
    EmployeeKey: 'nullToEmpty',
    TruckKey: 'nullToEmpty',
    DriverKey: 'nullToEmpty',
    LotKey: 'nullToEmpty',
    ServiceKey: 'nullToEmpty'
  }
};

/**
 * Core transformation functions for request data
 */
export const requestTransformers = {
  // Convert boolean to string ('1'/'0' or 'true'/'false')
  booleanToString: (value, format = 'numeric') => {
    if (value === null || value === undefined) return '';
    if (format === 'numeric') {
      return value ? '1' : '0';
    }
    return value ? 'true' : 'false';
  },
  
  // Convert number to string
  numberToString: (value) => {
    if (value === null || value === undefined) return '';
    return String(value);
  },
  
  // Convert null/undefined to empty string
  nullToEmpty: (value) => {
    return value === null || value === undefined ? '' : value;
  },
  
  // Ensure string format
  toString: (value) => {
    if (value === null || value === undefined) return '';
    return String(value);
  },
  
  // Transform arrays to delimited strings
  arrayToDelimited: (delimiter = ';') => (value) => {
    if (!Array.isArray(value)) return value;
    const result = value.join(delimiter);
    // Add trailing delimiter if needed (common TOPS pattern)
    return result.endsWith(delimiter) ? result : result + delimiter;
  },
  
  // Transform delimited strings to ensure proper format
  ensureDelimitedFormat: (delimiter = ';') => (value) => {
    if (typeof value !== 'string' || value === '') return value;
    return value.endsWith(delimiter) ? value : value + delimiter;
  },
  
  // Convert JavaScript Date to TOPS date format
  dateToTopsFormat: (value) => {
    if (!value) return '';
    if (value instanceof Date) {
      return value.toISOString().split('T')[0]; // YYYY-MM-DD format
    }
    return String(value);
  },
  
  // Convert JavaScript Date to TOPS datetime format
  datetimeToTopsFormat: (value) => {
    if (!value) return '';
    if (value instanceof Date) {
      return value.toISOString(); // ISO format
    }
    return String(value);
  },
  
  // Transform arrays of objects using a schema
  transformArray: (itemSchema) => (array) => {
    if (!Array.isArray(array)) return array;
    return array.map(item => {
      if (typeof itemSchema === 'object' && itemSchema !== null) {
        const transformed = { ...item };
        Object.keys(itemSchema).forEach(key => {
          if (key in transformed) {
            const transform = itemSchema[key];
            if (typeof transform === 'function') {
              transformed[key] = transform(transformed[key]);
            } else if (typeof transform === 'string') {
              transformed[key] = applyBuiltInRequestTransform(transformed[key], transform);
            }
          }
        });
        return transformed;
      }
      return item;
    });
  },
  
  // Conditional transformer - only transform if condition is met
  when: (condition, transformer) => (value) => {
    if (typeof condition === 'function' ? condition(value) : condition) {
      return typeof transformer === 'function' ? transformer(value) : value;
    }
    return value;
  }
};

// Helper function for built-in transform types
function applyBuiltInRequestTransform(value, type) {
  switch (type) {
    case 'string':
      return requestTransformers.toString(value);
    case 'booleanToString':
      return requestTransformers.booleanToString(value);
    case 'numberToString':
      return requestTransformers.numberToString(value);
    case 'nullToEmpty':
      return requestTransformers.nullToEmpty(value);
    case 'dateToTops':
      return requestTransformers.dateToTopsFormat(value);
    case 'datetimeToTops':
      return requestTransformers.datetimeToTopsFormat(value);
    default:
      return value;
  }
}

/**
 * Common transformation patterns for outgoing requests
 */
export const commonRequestTransforms = {
  // Call-related transformations
  callData: {
    Active: 'booleanToString',
    lCustomerKey: 'nullToEmpty',
    lEmployeeKey: 'nullToEmpty',
    lTruckKey: 'nullToEmpty',
    lDriverKey: 'nullToEmpty',
    fTaxRate: 'numberToString',
    fDiscountPct: 'numberToString',
    vc255DelimitedInfo: requestTransformers.ensureDelimitedFormat(';'),
    Retow: 'booleanToString'
  },
  
  // Customer data transformations
  customerData: {
    Active: 'booleanToString',
    TaxExempt: 'booleanToString',
    CreditLimit: 'numberToString',
    DiscountPct: 'numberToString'
  },
  
  // Employee data transformations
  employeeData: {
    Active: 'booleanToString',
    HourlyRate: 'numberToString',
    CommissionRate: 'numberToString'
  },
  
  // System tags transformations
  systemTagData: {
    Modifiable: 'booleanToString',
    Active: 'booleanToString'
  },
  
  // Pricing data transformations
  pricingData: {
    TaxRate: 'numberToString',
    TaxRateOverride: 'numberToString',
    DiscountPct: 'numberToString',
    Total: 'numberToString',
    TaxTotal: 'numberToString',
    DiscountTotal: 'numberToString'
  }
};

/**
 * Apply transformation schema to request data
 */
export function applyRequestTransformSchema(data, schema) {
  if (!schema || typeof schema !== 'object') return data;

  const transformedData = { ...data };

  Object.keys(schema).forEach(key => {
    if (key in transformedData) {
      const transformer = schema[key];
      if (typeof transformer === 'function') {
        transformedData[key] = transformer(transformedData[key]);
      } else if (typeof transformer === 'string') {
        transformedData[key] = applyBuiltInRequestTransform(transformedData[key], transformer);
      } else if (typeof transformer === 'object' && transformer !== null) {
        // Nested schema for objects/arrays
        if (Array.isArray(transformedData[key])) {
          transformedData[key] = transformedData[key].map(item => applyRequestTransformSchema(item, transformer));
        } else if (typeof transformedData[key] === 'object' && transformedData[key] !== null) {
          transformedData[key] = applyRequestTransformSchema(transformedData[key], transformer);
        }
      }
    }
  });

  return transformedData;
}

/**
 * Helper function to create a combined schema from multiple schemas
 */
export function combineRequestSchemas(...schemas) {
  return Object.assign({}, ...schemas);
}

/**
 * Helper function to create a schema for array transformation
 */
export function requestArraySchema(itemSchema) {
  return requestTransformers.transformArray(itemSchema);
}

/**
 * Helper function to create conditional transformations
 */
export function conditionalRequestTransform(condition, trueTransform, falseTransform = null) {
  return (value) => {
    const shouldTransform = typeof condition === 'function' ? condition(value) : condition;
    if (shouldTransform) {
      return typeof trueTransform === 'function' ? trueTransform(value) : trueTransform;
    }
    return falseTransform ? (typeof falseTransform === 'function' ? falseTransform(value) : falseTransform) : value;
  };
}
