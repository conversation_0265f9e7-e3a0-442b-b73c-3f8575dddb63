# TOPS API Response Transformation System

This document describes the new opt-in response transformation system for TOPS API calls, designed to replace manual `responseMiddleware` callbacks with clean, reusable transformation strategies.

## Overview

The TOPS API typically returns data with string values that need to be converted to appropriate JavaScript types (numbers, booleans, etc.). Previously, this was handled with manual `responseMiddleware` callbacks. The new system provides two clean, reusable approaches.

## Migration from Old System

### Before (Manual responseMiddleware)
```javascript
new StandardRequest(context, {
  noun: 'TOPSCompany',
  verb: 'GetEmployees',
  data: { /* ... */ }
})
.responseMiddleware(response => {
  response.forEach(item => {
    if ('Key' in item) item.Key = Number(item.Key);
    if ('Active' in item) item.Active = item.Active === 'true';
  });
})
.success(props.callback);
```

### After (Pre-built common transform - Old API)
```javascript
new StandardRequest(context, {
  noun: 'TOPSCompany',
  verb: 'GetEmployees',
  data: { /* ... */ }
})
.transform(commonResponseTransforms.employeeList)
.success(props.callback);
```

### After (New TOPS API Client)
```javascript
const response = await tops.TOPSCompany.GetEmployees({});
const transformedData = applyResponseTransformSchema(response.data, commonResponseTransforms.employeeList);
```

## Transformation Strategies

### 1. Schema-Based Transformation

Define explicit transformation schemas:

**With Old API (StandardRequest/CacheableRequest):**
```javascript
.transform({
  Key: 'number',
  Active: 'boolean',
  Data: 'json',
  Items: arraySchema({
    ItemKey: 'number',
    ItemActive: 'boolean'
  })
})
```

**With New TOPS API Client:**
```javascript
const response = await tops.SomeNoun.SomeVerb({});
const transformedData = applyResponseTransformSchema(response.data, {
  Key: 'number',
  Active: 'boolean',
  Data: 'json',
  Items: arraySchema({
    ItemKey: 'number',
    ItemActive: 'boolean'
  })
});
```

**Built-in transform types:**
- `'number'` - Convert to number
- `'boolean'` - Convert to boolean
- `'json'` - Parse JSON
- `'string'` - Convert to string
- `'float'` - Convert to float
- `'int'` - Convert to integer

### 2. Pre-built Common Transforms

Use pre-built schemas for common response patterns:

**With Old API (StandardRequest/CacheableRequest):**
```javascript
// Employee/driver/truck lists
.transform(commonResponseTransforms.employeeList)

// Status lists
.transform(commonResponseTransforms.statusList)

// Report lists with boolean flags
.transform(commonResponseTransforms.reportList)

// Inspection items with nested arrays
.transform(commonResponseTransforms.inspectionItems)

// Location data with coordinates
.transform(commonResponseTransforms.locationData)
```

**With New TOPS API Client:**
```javascript
const response = await tops.TOPSCompany.GetEmployees({});
const transformedData = applyResponseTransformSchema(response.data, commonResponseTransforms.employeeList);

const reportResponse = await tops.TOPSCompany.GetReports({});
const transformedReports = applyResponseTransformSchema(reportResponse.data, commonResponseTransforms.reportList);
```

## Combining Approaches

You can combine schema-based transforms with pre-built common transforms:

```javascript
new StandardRequest(context, { /* ... */ })
  .transform({
    ...commonResponseTransforms.employeeList,  // Use pre-built transform
    SpecialField: 'json',              // Add specific transformations
    CustomData: (value) => value.toUpperCase()
  })
  .success(callback);
```

## Pre-built Schema Collections

### transformSchemas
- `keyFields` - Common Key field transformations
- `booleanFields` - Common boolean field transformations
- `numericFields` - Common numeric field transformations
- `coordinates` - Geographic coordinate transformations
- `labelSettings` - Label/printing setting transformations

### commonResponseTransforms
- `employeeList` - Employee/driver/truck arrays
- `statusList` - Status arrays
- `reportList` - Report arrays with boolean flags
- `inspectionItems` - Inspection items with nested arrays
- `lienSteps` - Lien step arrays
- `locationData` - Location data with coordinates

## Utility Functions

### combineSchemas(...schemas)
Combine multiple schemas into one:

```javascript
const combinedSchema = combineSchemas(
  transformSchemas.keyFields,
  transformSchemas.booleanFields,
  { CustomField: 'json' }
);
```

### arraySchema(itemSchema)
Transform arrays of objects:

```javascript
const listSchema = arraySchema({
  Key: 'number',
  Active: 'boolean'
});
```

## Best Practices

1. **Use pre-built common transforms when available** - They handle common patterns consistently
2. **Use schema-based transforms for specific needs** - When you need custom transformation logic
3. **Combine approaches when needed** - Mix pre-built and custom transforms
4. **Test transformations thoroughly** - Ensure data types are correctly converted
5. **Keep transforms simple and focused** - Each transform should have a single responsibility

## Performance Notes

- Transformations are applied in-place to avoid object copying
- Auto-transform uses efficient field name checking
- Schema-based transforms only process specified fields
- Nested transformations handle arrays and objects recursively

## Error Handling

- JSON parsing failures fall back to original value
- Number conversion failures fall back to original value
- Boolean conversion handles various truthy string formats
- Transformers are defensive against null/undefined values
